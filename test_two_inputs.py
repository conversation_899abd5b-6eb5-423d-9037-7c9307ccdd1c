#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试两个输入框的功能
"""

import uiautomation as auto
import time
import pyperclip

def safe_input_text(control, text, method="clipboard"):
    """
    安全的文本输入方法
    """
    try:
        if not control.Exists():
            print("❌ 控件不存在")
            return False
            
        control.Click()
        time.sleep(0.3)
        
        if method == "clipboard":
            pyperclip.copy(text)
            control.SendKeys("{Ctrl}a")
            time.sleep(0.1)
            control.SendKeys("{Ctrl}v")
            time.sleep(0.2)
            print(f"✅ 剪切板方式输入: {text}")
            return True
            
    except Exception as e:
        print(f"❌ 输入失败: {e}")
        return False
    
    return False

def test_two_input_boxes():
    """
    测试两个输入框
    """
    print("🔍 正在查找微信添加好友对话框...")
    
    # 查找微信主窗口
    wechat_window = auto.WindowControl(
        searchDepth=1, ClassName="WeChatMainWndForPC", Name="微信"
    )
    
    if not wechat_window.Exists():
        print("❌ 未找到微信主窗口")
        return
    
    # 查找添加好友对话框 - 使用最有效的方法
    verify_box = wechat_window.WindowControl(
        searchDepth=1, ClassName="WeUIDialog", Name="添加朋友请求"
    )
    
    if not verify_box.Exists():
        print("❌ 未找到添加好友对话框")
        return
    
    print("✅ 找到添加好友对话框")
    
    # 等待对话框完全加载
    print("⏳ 等待对话框完全加载...")
    time.sleep(1.5)
    
    # 查找所有EditControl
    print("🔍 查找所有EditControl...")
    all_edit_controls = []
    
    def collect_edit_controls(parent, depth=0):
        if depth > 4:
            return
        try:
            children = parent.GetChildren()
            for child in children:
                if child.ControlTypeName == "EditControl" and child.Exists():
                    all_edit_controls.append(child)
                collect_edit_controls(child, depth + 1)
        except:
            pass
    
    collect_edit_controls(verify_box)
    
    print(f"✅ 找到 {len(all_edit_controls)} 个EditControl")
    
    # 分析和识别输入框
    remarkBox = None  # 备注输入框
    messageBox = None  # 消息输入框
    
    for i, control in enumerate(all_edit_controls):
        control_name = control.Name
        print(f"   EditControl {i+1}: '{control_name}'")
        
        if control_name.startswith("我是"):
            messageBox = control
            print(f"   ✅ 这是消息输入框")
        elif control_name == "" or len(control_name) < 10:
            remarkBox = control
            print(f"   ✅ 这是备注输入框")
    
    # 如果没有明确识别，按顺序分配
    if len(all_edit_controls) >= 2 and (remarkBox is None or messageBox is None):
        remarkBox = all_edit_controls[0]  # 第一个通常是备注
        messageBox = all_edit_controls[1]  # 第二个通常是消息
        print("⚠️  按顺序分配：第1个=备注框，第2个=消息框")
    
    # 测试输入
    test_remark = "测试备注"
    test_message = "测试消息内容"
    
    print(f"\n📝 开始测试输入...")
    
    # 测试备注输入框
    if remarkBox:
        print(f"\n🔄 测试备注输入框: '{test_remark}'")
        if safe_input_text(remarkBox, test_remark):
            print("✅ 备注输入成功!")
        else:
            print("❌ 备注输入失败!")
        time.sleep(1)
    else:
        print("❌ 未找到备注输入框")
    
    # 测试消息输入框
    if messageBox:
        print(f"\n🔄 测试消息输入框: '{test_message}'")
        if safe_input_text(messageBox, test_message):
            print("✅ 消息输入成功!")
        else:
            print("❌ 消息输入失败!")
        time.sleep(1)
    else:
        print("❌ 未找到消息输入框")
    
    # 询问是否清空
    print(f"\n📋 测试完成!")
    print("请检查两个输入框的内容是否正确")
    
    clear_choice = input("\n是否清空所有输入框？(y/n): ").strip().lower()
    if clear_choice == 'y':
        if remarkBox:
            try:
                remarkBox.Click()
                time.sleep(0.2)
                remarkBox.SendKeys("{Ctrl}a{Delete}")
                print("✅ 已清空备注输入框")
            except Exception as e:
                print(f"❌ 清空备注输入框失败: {e}")
        
        if messageBox:
            try:
                messageBox.Click()
                time.sleep(0.2)
                messageBox.SendKeys("{Ctrl}a{Delete}")
                print("✅ 已清空消息输入框")
            except Exception as e:
                print(f"❌ 清空消息输入框失败: {e}")
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    print("🚀 两个输入框测试工具")
    print("请确保:")
    print("1. 微信已启动并登录")
    print("2. 已打开添加好友对话框")
    print("3. 可以看到备注和消息两个输入框")
    
    input("\n按回车键开始测试...")
    test_two_input_boxes()

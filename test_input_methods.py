#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同的文本输入方法
"""

import uiautomation as auto
import time
import pyperclip
import pya<PERSON>gu<PERSON>

def test_input_methods():
    """
    测试各种输入方法
    """
    print("🔍 正在查找微信窗口...")
    
    # 查找微信窗口
    wechat_window = auto.WindowControl(
        searchDepth=1, ClassName="WeChatMainWndForPC", Name="微信"
    )
    
    if not wechat_window.Exists():
        print("❌ 未找到微信窗口，请确保微信已启动")
        return
    
    print("✅ 找到微信窗口")
    wechat_window.SetActive()
    
    # 查找任何输入框进行测试
    print("🔍 正在查找输入框...")
    
    # 尝试查找聊天输入框
    chat_input = wechat_window.EditControl(Name="输入")
    if chat_input.Exists():
        print("✅ 找到聊天输入框，开始测试...")
        test_text = "测试消息"
        
        # 测试剪切板方法
        print("🔄 测试剪切板方法...")
        try:
            chat_input.Click()
            time.sleep(0.5)
            pyperclip.copy(test_text)
            chat_input.SendKeys("{Ctrl}a")
            time.sleep(0.2)
            chat_input.SendKeys("{Ctrl}v")
            print("✅ 剪切板方法成功")
            time.sleep(2)
            chat_input.SendKeys("{Ctrl}a")
            chat_input.SendKeys("{Delete}")
        except Exception as e:
            print(f"❌ 剪切板方法失败: {e}")
        
        # 测试SendKeys方法
        print("🔄 测试SendKeys方法...")
        try:
            chat_input.Click()
            time.sleep(0.5)
            chat_input.SendKeys("{Ctrl}a")
            chat_input.SendKeys(test_text)
            print("✅ SendKeys方法成功")
            time.sleep(2)
            chat_input.SendKeys("{Ctrl}a")
            chat_input.SendKeys("{Delete}")
        except Exception as e:
            print(f"❌ SendKeys方法失败: {e}")
        
        # 测试PyAutoGUI方法
        print("🔄 测试PyAutoGUI方法...")
        try:
            chat_input.Click()
            time.sleep(0.5)
            chat_input.SendKeys("{Ctrl}a")
            pyautogui.typewrite(test_text, interval=0.05)
            print("✅ PyAutoGUI方法成功")
            time.sleep(2)
            chat_input.SendKeys("{Ctrl}a")
            chat_input.SendKeys("{Delete}")
        except Exception as e:
            print(f"❌ PyAutoGUI方法失败: {e}")
            
    else:
        print("❌ 未找到输入框，请打开一个聊天窗口")

if __name__ == "__main__":
    test_input_methods()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查对话框是否正确
"""

import uiautomation as auto
import time

def check_dialog():
    """
    检查对话框
    """
    print("🔍 检查微信对话框...")
    
    # 查找微信主窗口
    wechat_window = auto.WindowControl(
        searchDepth=1, ClassName="WeChatMainWndForPC", Name="微信"
    )
    
    if not wechat_window.Exists():
        print("❌ 未找到微信主窗口")
        return
    
    print("✅ 找到微信主窗口")
    
    # 列出微信主窗口下的所有子窗口
    print("\n📋 微信主窗口下的所有子窗口:")
    try:
        children = wechat_window.GetChildren()
        for i, child in enumerate(children):
            print(f"{i+1}. {child.ClassName} - '{child.Name}' (可见:{child.IsVisible})")
            
            # 如果包含Dialog或添加朋友相关的，详细查看
            if "Dialog" in child.ClassName or "添加" in child.Name or "朋友" in child.Name:
                print(f"   ⭐ 这可能是我们要找的对话框!")
                
                # 查看这个对话框的子控件
                try:
                    dialog_children = child.GetChildren()
                    for j, dialog_child in enumerate(dialog_children):
                        print(f"     {j+1}. {dialog_child.ControlTypeName} - '{dialog_child.Name}'")
                        
                        if dialog_child.ControlTypeName == "EditControl":
                            print(f"       ⭐⭐⭐ 找到EditControl: '{dialog_child.Name}' ⭐⭐⭐")
                except:
                    print("     无法获取对话框子控件")
    except Exception as e:
        print(f"❌ 获取子窗口失败: {e}")
    
    # 尝试不同的对话框查找方法
    print("\n🔍 尝试不同的对话框查找方法:")
    
    methods = [
        ("方法1: WeUIDialog + 添加朋友请求", lambda: wechat_window.WindowControl(searchDepth=1, ClassName="WeUIDialog", Name="添加朋友请求")),
        ("方法2: 任何WeUIDialog", lambda: wechat_window.WindowControl(searchDepth=1, ClassName="WeUIDialog")),
        ("方法3: 包含'添加'的窗口", lambda: wechat_window.WindowControl(NameRegex=".*添加.*")),
        ("方法4: 包含'朋友'的窗口", lambda: wechat_window.WindowControl(NameRegex=".*朋友.*")),
        ("方法5: 任何Dialog类", lambda: wechat_window.WindowControl(ClassNameRegex=".*Dialog.*")),
    ]
    
    found_dialogs = []
    
    for method_name, method_func in methods:
        try:
            dialog = method_func()
            if dialog.Exists():
                print(f"✅ {method_name} - 成功!")
                print(f"   类名: {dialog.ClassName}")
                print(f"   名称: '{dialog.Name}'")
                found_dialogs.append((method_name, dialog))
            else:
                print(f"❌ {method_name} - 失败")
        except Exception as e:
            print(f"❌ {method_name} - 异常: {e}")
    
    # 对找到的每个对话框，查找EditControl
    if found_dialogs:
        print(f"\n🔍 在找到的 {len(found_dialogs)} 个对话框中查找EditControl:")
        
        for method_name, dialog in found_dialogs:
            print(f"\n📋 {method_name}:")
            try:
                # 查找EditControl
                edit_control = dialog.EditControl()
                if edit_control.Exists():
                    print(f"   ✅ 找到EditControl: '{edit_control.Name}'")
                    
                    # 尝试输入测试
                    try:
                        edit_control.Click()
                        time.sleep(0.2)
                        edit_control.SendKeys("测试输入")
                        time.sleep(0.5)
                        print(f"   ✅ 输入测试成功!")
                        edit_control.SendKeys("{Ctrl}a{Delete}")  # 清空
                    except Exception as e:
                        print(f"   ❌ 输入测试失败: {e}")
                else:
                    print(f"   ❌ 没有找到EditControl")
                    
                    # 列出所有子控件
                    print(f"   📋 所有子控件:")
                    children = dialog.GetChildren()
                    for i, child in enumerate(children):
                        print(f"     {i+1}. {child.ControlTypeName} - '{child.Name}'")
                        
            except Exception as e:
                print(f"   ❌ 查找EditControl异常: {e}")
    else:
        print("\n❌ 没有找到任何对话框!")
        print("请确保:")
        print("1. 微信已登录")
        print("2. 已打开添加好友对话框")
        print("3. 对话框完全显示")

if __name__ == "__main__":
    print("🚀 对话框检查工具")
    print("请确保添加好友对话框已打开")
    input("按回车键开始检查...")
    check_dialog()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试动态输入框定位和输入
"""

import uiautomation as auto
import time
import pyperclip

def safe_input_text(control, text, method="clipboard"):
    """
    安全的文本输入方法
    """
    try:
        if not control.Exists():
            print("❌ 控件不存在")
            return False
            
        control.Click()
        time.sleep(0.3)
        
        if method == "clipboard":
            pyperclip.copy(text)
            control.SendKeys("{Ctrl}a")
            time.sleep(0.1)
            control.SendKeys("{Ctrl}v")
            time.sleep(0.2)
            print(f"✅ 剪切板方式输入: {text}")
            return True
            
        elif method == "sendkeys":
            control.SendKeys("{Ctrl}a")
            time.sleep(0.1)
            control.SendKeys(text)
            time.sleep(0.2)
            print(f"✅ SendKeys方式输入: {text}")
            return True
            
    except Exception as e:
        print(f"❌ 输入失败: {e}")
        return False
    
    return False

def test_dynamic_input_box():
    """
    测试动态输入框的定位和输入
    """
    print("🔍 正在查找微信添加好友对话框...")
    
    # 查找微信主窗口
    wechat_window = auto.WindowControl(
        searchDepth=1, ClassName="WeChatMainWndForPC", Name="微信"
    )
    
    if not wechat_window.Exists():
        print("❌ 未找到微信主窗口")
        return
    
    # 查找添加好友对话框
    verify_box = wechat_window.WindowControl(
        searchDepth=1, ClassName="WeUIDialog", Name="添加朋友请求"
    )
    
    if not verify_box.Exists():
        print("❌ 未找到添加好友对话框")
        return
    
    print("✅ 找到添加好友对话框")
    
    # 等待对话框完全加载
    print("⏳ 等待对话框完全加载...")
    time.sleep(1)
    
    # 查找所有EditControl
    print("🔍 查找所有EditControl...")
    edit_controls = []
    
    def find_edit_controls(parent, depth=0):
        if depth > 3:
            return
        try:
            children = parent.GetChildren()
            for child in children:
                if child.ControlTypeName == "EditControl" and child.Exists():
                    edit_controls.append(child)
                    print(f"   找到EditControl: '{child.Name}' (深度:{depth})")
                find_edit_controls(child, depth + 1)
        except:
            pass
    
    find_edit_controls(verify_box)
    
    if not edit_controls:
        print("❌ 未找到任何EditControl")
        return
    
    print(f"\n📋 总共找到 {len(edit_controls)} 个EditControl:")
    for i, control in enumerate(edit_controls, 1):
        print(f"   {i}. '{control.Name}' (可见:{control.IsVisible}, 启用:{control.IsEnabled})")
    
    # 识别消息输入框（通常是包含"我是"开头的）
    messageBox = None
    for control in edit_controls:
        if control.Name.startswith("我是"):
            messageBox = control
            print(f"\n✅ 识别消息输入框: '{control.Name}'")
            break
    
    if messageBox is None and edit_controls:
        messageBox = edit_controls[0]
        print(f"\n⚠️  使用第一个EditControl作为消息输入框: '{messageBox.Name}'")
    
    if messageBox is None:
        print("❌ 无法确定消息输入框")
        return
    
    # 测试输入
    test_message = "这是测试消息"
    print(f"\n🔄 开始测试输入: '{test_message}'")
    
    methods = ["clipboard", "sendkeys"]
    input_success = False
    
    for method in methods:
        print(f"\n🔄 尝试使用 {method} 方式输入...")
        if safe_input_text(messageBox, test_message, method):
            input_success = True
            break
        time.sleep(0.5)
    
    if input_success:
        print(f"\n✅ 输入测试成功!")
        print("请检查输入框中的内容是否正确")
        
        # 询问是否清空
        clear_choice = input("\n是否清空输入框？(y/n): ").strip().lower()
        if clear_choice == 'y':
            try:
                messageBox.Click()
                time.sleep(0.2)
                messageBox.SendKeys("{Ctrl}a")
                messageBox.SendKeys("{Delete}")
                print("✅ 已清空输入框")
            except Exception as e:
                print(f"❌ 清空失败: {e}")
    else:
        print("\n❌ 输入测试失败")
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    print("🚀 动态输入框测试工具")
    print("请确保:")
    print("1. 微信已启动并登录")
    print("2. 已打开添加好友对话框")
    print("3. 对话框完全加载完毕")
    
    input("\n按回车键开始测试...")
    test_dynamic_input_box()

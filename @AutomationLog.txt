2025-08-05 01:38:24.197 wechat_automation.py[32] run_automation -> Find Control Timeout(10s): {Name: '添加朋友', ControlType: ButtonControl}
2025-08-05 01:39:18.193 wechat_automation.py[32] run_automation -> Find Control Timeout(10s): {Name: '添加朋友', ControlType: ButtonControl}
2025-08-05 01:51:28.142 wechat_automation.py[30] add_wechat_contact -> Find Control Timeout(10s): {Name: '添加朋友', ControlType: ButtonControl}
2025-08-05 01:55:16.510 wechat_automation.py[36] add_wechat_contact -> Find Control Timeout(10s): {Name: '搜索', ControlType: EditControl}
2025-08-05 01:57:35.766 wechat_automation.py[30] add_wechat_contact -> Find Control Timeout(10s): {Name: '添加朋友', ControlType: ButtonControl}
2025-08-05 15:19:04.861 wechat_automation.py[53] add_wechat_contact -> Find Control Timeout(10s): {Name: '添加到通讯录', ControlType: ButtonControl}
2025-08-05 15:23:30.196 wechat_automation.py[46] add_wechat_contact -> Find Control Timeout(10s): {Name: '搜索', ControlType: ButtonControl}
2025-08-05 15:43:01.138 wechat_automation.py[30] add_wechat_contact -> Find Control Timeout(10s): {Name: '添加朋友', ControlType: ButtonControl}
2025-08-05 15:43:28.807 wechat_automation.py[46] add_wechat_contact -> Find Control Timeout(10s): {Name: '搜索', ControlType: ButtonControl}
2025-08-05 15:49:27.183 wechat_automation.py[46] add_wechat_contact -> Find Control Timeout(10s): {Name: '搜索', ControlType: TextControl}
2025-08-05 15:51:59.104 wechat_automation.py[31] add_wechat_contact -> Find Control Timeout(10s): {Name: '添加朋友', ControlType: ButtonControl}
2025-08-05 16:02:35.265 wechat_automation.py[31] add_wechat_contact -> Find Control Timeout(10s): {Name: '添加朋友', ControlType: ButtonControl}
2025-08-05 17:16:58.175 wechat_automation.py[66] add_wechat_contact -> Find Control Timeout(10s): {Name: '添加到通讯录', ControlType: ButtonControl}
2025-08-05 18:12:18.541 wechat_automation.py[39] add_wechat_contact -> Find Control Timeout(10s): {Name: '添加朋友', ControlType: ButtonControl}
2025-08-05 18:13:01.617 wechat_automation.py[87] add_wechat_contact -> Find Control Timeout(10s): {NameRegex: '验证信息|我是.*', timeout: 10, ControlType: EditControl}
2025-08-05 18:14:33.150 wechat_automation.py[87] add_wechat_contact -> Find Control Timeout(10s): {NameRegex: '我是.*', timeout: 10, ControlType: EditControl}
2025-08-05 18:26:38.923 wechat_automation.py[92] add_wechat_contact -> Find Control Timeout(10s): {NameRegex: '我是.*', timeout: 10, ControlType: EditControl}
2025-08-05 18:27:33.632 wechat_automation.py[92] add_wechat_contact -> Find Control Timeout(10s): {NameRegex: '我是.*', timeout: 10, ControlType: EditControl}
2025-08-05 18:36:54.112 wechat_automation.py[89] add_wechat_contact -> Find Control Timeout(10s): {Name: '取消', ControlType: ButtonControl}
2025-08-05 18:55:49.616 wechat_automation.py[93] add_wechat_contact -> Find Control Timeout(10s): {Name: '我是.*', ControlType: EditControl}
2025-08-05 22:00:42.957 wechat_automation.py[93] add_wechat_contact -> Find Control Timeout(10s): {Name: '我是.*', ControlType: EditControl}
2025-08-05 22:01:38.672 wechat_automation.py[93] add_wechat_contact -> Find Control Timeout(10s): {Name: '*我是.*', ControlType: EditControl}
2025-08-06 00:25:44.154 wechat_automation.py[120] add_wechat_contact -> Find Control Timeout(10s): {ControlType: EditControl}
2025-08-06 01:06:41.275 wechat_automation.py[44] add_wechat_contact -> Find Control Timeout(10s): {Name: '添加朋友', ControlType: ButtonControl}

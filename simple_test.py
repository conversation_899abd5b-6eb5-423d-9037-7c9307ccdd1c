#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简单的测试 - 完全复制check_dialog.py的成功方法
"""

import uiautomation as auto
import time
import pyperclip

def simple_test():
    """
    最简单的测试
    """
    print("🔍 开始最简单的测试...")
    
    # 查找微信主窗口
    wechat_window = auto.WindowControl(
        searchDepth=1, ClassName="WeChatMainWndForPC", Name="微信"
    )
    
    if not wechat_window.Exists():
        print("❌ 未找到微信主窗口")
        return
    
    print("✅ 找到微信主窗口")
    
    # 使用check_dialog.py中验证成功的方法1
    verify_box = wechat_window.WindowControl(
        searchDepth=1, ClassName="WeUIDialog", Name="添加朋友请求"
    )
    
    if not verify_box.Exists():
        print("❌ 未找到添加好友对话框")
        return
    
    print("✅ 找到添加好友对话框")
    
    # 完全复制check_dialog.py的成功代码
    try:
        # 查找EditControl - 和check_dialog.py完全一样
        edit_control = verify_box.EditControl()
        if edit_control.Exists():
            print(f"✅ 找到EditControl: '{edit_control.Name}'")
            
            # 测试输入 - 使用剪切板方式
            try:
                print("🔄 测试剪切板输入...")
                edit_control.Click()
                time.sleep(0.3)
                
                test_text = "测试消息"
                pyperclip.copy(test_text)
                edit_control.SendKeys("{Ctrl}a")
                time.sleep(0.1)
                edit_control.SendKeys("{Ctrl}v")
                time.sleep(0.5)
                
                print(f"✅ 剪切板输入成功: {test_text}")
                
                # 清空
                edit_control.SendKeys("{Ctrl}a{Delete}")
                print("✅ 已清空")
                
            except Exception as e:
                print(f"❌ 剪切板输入失败: {e}")
                
                # 尝试SendKeys方式
                try:
                    print("🔄 尝试SendKeys方式...")
                    edit_control.Click()
                    time.sleep(0.2)
                    edit_control.SendKeys("测试输入")
                    time.sleep(0.5)
                    print(f"✅ SendKeys输入成功!")
                    edit_control.SendKeys("{Ctrl}a{Delete}")  # 清空
                except Exception as e2:
                    print(f"❌ SendKeys输入也失败: {e2}")
        else:
            print(f"❌ 没有找到EditControl")
            
    except Exception as e:
        print(f"❌ 查找EditControl异常: {e}")
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    print("🚀 最简单测试工具")
    print("完全复制check_dialog.py的成功方法")
    input("按回车键开始...")
    simple_test()

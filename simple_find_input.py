#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简单直接的输入框查找方法
"""

import uiautomation as auto
import time

def find_all_controls_simple():
    """
    最简单粗暴的方法 - 找到所有控件
    """
    print("🔍 开始最简单的查找方法...")
    
    # 查找微信主窗口
    wechat_window = auto.WindowControl(
        searchDepth=1, ClassName="WeChatMainWndForPC", Name="微信"
    )
    
    if not wechat_window.Exists():
        print("❌ 未找到微信主窗口")
        return
    
    print("✅ 找到微信主窗口")
    
    # 查找添加好友对话框
    verify_box = wechat_window.WindowControl(
        searchDepth=1, ClassName="WeUIDialog", Name="添加朋友请求"
    )
    
    if not verify_box.Exists():
        print("❌ 未找到添加好友对话框")
        # 尝试查找任何对话框
        print("🔄 尝试查找任何对话框...")
        all_windows = wechat_window.GetChildren()
        for window in all_windows:
            print(f"   窗口: {window.ClassName} - '{window.Name}'")
            if "Dialog" in window.ClassName or "添加" in window.Name:
                verify_box = window
                print(f"✅ 找到可能的对话框: {window.Name}")
                break
        
        if not verify_box.Exists():
            print("❌ 完全找不到对话框")
            return
    
    print("✅ 找到对话框")
    
    # 最简单粗暴的方法：打印所有控件
    print("\n" + "="*60)
    print("📋 所有控件列表:")
    print("="*60)
    
    def print_all_controls(parent, depth=0, max_depth=5):
        if depth > max_depth:
            return
        
        indent = "  " * depth
        try:
            children = parent.GetChildren()
            for i, child in enumerate(children):
                try:
                    control_info = f"{indent}{i+1}. {child.ControlTypeName}"
                    if child.Name:
                        control_info += f" - '{child.Name}'"
                    if hasattr(child, 'Value') and child.Value:
                        control_info += f" [值: '{child.Value}']"
                    
                    control_info += f" (可见:{child.IsVisible}, 启用:{child.IsEnabled})"
                    print(control_info)
                    
                    # 如果是EditControl，特别标记
                    if child.ControlTypeName == "EditControl":
                        print(f"{indent}   ⭐⭐⭐ 这是EditControl! ⭐⭐⭐")
                        
                        # 尝试点击和输入测试
                        try:
                            print(f"{indent}   🔄 测试这个EditControl...")
                            child.Click()
                            time.sleep(0.3)
                            child.SendKeys("测试")
                            time.sleep(0.5)
                            child.SendKeys("{Ctrl}a{Delete}")  # 清空
                            print(f"{indent}   ✅ 这个EditControl可以输入!")
                        except Exception as e:
                            print(f"{indent}   ❌ 这个EditControl输入失败: {e}")
                    
                    # 递归查找子控件
                    print_all_controls(child, depth + 1, max_depth)
                    
                except Exception as e:
                    print(f"{indent}{i+1}. 错误: {e}")
        except Exception as e:
            print(f"{indent}获取子控件失败: {e}")
    
    print_all_controls(verify_box)
    
    print("\n" + "="*60)
    print("🔍 直接查找方法:")
    print("="*60)
    
    # 方法1: 最简单的EditControl查找
    try:
        edit1 = verify_box.EditControl()
        if edit1.Exists():
            print(f"✅ 方法1成功: EditControl() - '{edit1.Name}'")
        else:
            print("❌ 方法1失败: EditControl()")
    except Exception as e:
        print(f"❌ 方法1异常: {e}")
    
    # 方法2: 深度搜索
    try:
        edit2 = verify_box.EditControl(searchDepth=10)
        if edit2.Exists():
            print(f"✅ 方法2成功: EditControl(searchDepth=10) - '{edit2.Name}'")
        else:
            print("❌ 方法2失败: EditControl(searchDepth=10)")
    except Exception as e:
        print(f"❌ 方法2异常: {e}")
    
    # 方法3: 查找所有EditControl
    try:
        all_edits = []
        def find_edits(parent, depth=0):
            if depth > 10:
                return
            try:
                children = parent.GetChildren()
                for child in children:
                    if child.ControlTypeName == "EditControl":
                        all_edits.append(child)
                    find_edits(child, depth + 1)
            except:
                pass
        
        find_edits(verify_box)
        
        if all_edits:
            print(f"✅ 方法3成功: 找到 {len(all_edits)} 个EditControl")
            for i, edit in enumerate(all_edits):
                print(f"   EditControl {i+1}: '{edit.Name}'")
        else:
            print("❌ 方法3失败: 没找到任何EditControl")
    except Exception as e:
        print(f"❌ 方法3异常: {e}")
    
    print("\n✅ 查找完成!")

if __name__ == "__main__":
    print("🚀 最简单直接的输入框查找工具")
    print("请确保添加好友对话框已打开")
    input("按回车键开始...")
    find_all_controls_simple()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信控件调试工具
用于分析微信添加好友对话框的控件结构
"""

import uiautomation as auto
import time

def print_control_tree(control, depth=0, max_depth=4):
    """
    递归打印控件树结构
    """
    if depth > max_depth:
        return
    
    indent = "  " * depth
    try:
        if control.Exists():
            control_info = f"{indent}├─ {control.ControlTypeName}: '{control.Name}'"
            if control.IsVisible:
                control_info += " [可见]"
            if control.IsEnabled:
                control_info += " [启用]"
            if hasattr(control, 'Value') and control.Value:
                control_info += f" 值:'{control.Value}'"
            
            print(control_info)
            
            # 如果是EditControl或TextControl，特别标记
            if "Edit" in control.ControlTypeName or "Text" in control.ControlTypeName:
                print(f"{indent}   ⭐ 这是一个可能的输入控件!")
                # 尝试获取更多信息
                try:
                    if hasattr(control, 'GetPattern'):
                        print(f"{indent}   📝 支持的模式: {control.GetPattern}")
                except:
                    pass
            
            # 递归打印子控件
            try:
                children = control.GetChildren()
                for child in children:
                    print_control_tree(child, depth + 1, max_depth)
            except:
                pass
        else:
            print(f"{indent}├─ {control.ControlTypeName}: [不存在]")
    except Exception as e:
        print(f"{indent}├─ 错误: {e}")

def find_wechat_add_friend_dialog():
    """
    查找微信添加好友对话框
    """
    print("🔍 正在查找微信窗口...")
    
    # 查找微信主窗口
    wechat_window = auto.WindowControl(
        searchDepth=1, ClassName="WeChatMainWndForPC", Name="微信"
    )
    
    if not wechat_window.Exists():
        print("❌ 未找到微信主窗口")
        return None
    
    print("✅ 找到微信主窗口")
    wechat_window.SetActive()
    
    # 查找添加好友对话框
    print("🔍 正在查找添加好友对话框...")
    
    # 方法1: 直接查找WeUIDialog
    verify_box = wechat_window.WindowControl(
        searchDepth=1, ClassName="WeUIDialog", Name="添加朋友请求"
    )
    
    if verify_box.Exists():
        print("✅ 找到添加好友对话框 (WeUIDialog)")
        return verify_box
    
    # 方法2: 查找任何包含"添加朋友"的对话框
    dialogs = wechat_window.GetChildren()
    for dialog in dialogs:
        if "添加朋友" in dialog.Name or "WeUIDialog" in dialog.ClassName:
            print(f"✅ 找到可能的对话框: {dialog.Name} ({dialog.ClassName})")
            return dialog
    
    print("❌ 未找到添加好友对话框")
    return None

def analyze_input_controls(verify_box):
    """
    分析对话框中的输入控件
    """
    print("\n🔍 分析输入控件...")
    
    # 查找所有可能的输入控件
    input_controls = []
    
    try:
        # 方法1: 查找EditControl
        edit_controls = verify_box.GetChildren()
        for control in edit_controls:
            if "Edit" in control.ControlTypeName:
                input_controls.append(("EditControl", control))
        
        # 方法2: 深度搜索EditControl
        try:
            edit_control = verify_box.EditControl(searchDepth=5)
            if edit_control.Exists():
                input_controls.append(("深度搜索EditControl", edit_control))
        except:
            pass
        
        # 方法3: 查找TextControl
        try:
            text_control = verify_box.TextControl(searchDepth=5)
            if text_control.Exists():
                input_controls.append(("TextControl", text_control))
        except:
            pass
        
        # 方法4: 查找包含"我是"的控件
        try:
            message_control = verify_box.Control(NameRegex="我是.*", searchDepth=5)
            if message_control.Exists():
                input_controls.append(("包含'我是'的控件", message_control))
        except:
            pass
        
    except Exception as e:
        print(f"❌ 分析输入控件时出错: {e}")
    
    if input_controls:
        print(f"✅ 找到 {len(input_controls)} 个可能的输入控件:")
        for i, (method, control) in enumerate(input_controls, 1):
            print(f"\n📝 输入控件 {i} ({method}):")
            print(f"   类型: {control.ControlTypeName}")
            print(f"   名称: '{control.Name}'")
            print(f"   可见: {control.IsVisible}")
            print(f"   启用: {control.IsEnabled}")
            try:
                print(f"   位置: {control.BoundingRectangle}")
            except:
                pass
    else:
        print("❌ 未找到任何输入控件")

def main():
    """
    主函数
    """
    print("🚀 微信控件调试工具启动")
    print("请确保:")
    print("1. 微信已启动并登录")
    print("2. 已打开添加好友对话框")
    print("3. 对话框中显示有输入框")
    
    input("\n按回车键继续...")
    
    # 查找对话框
    verify_box = find_wechat_add_friend_dialog()
    if not verify_box:
        return
    
    print("\n" + "="*50)
    print("📊 完整控件树结构:")
    print("="*50)
    print_control_tree(verify_box)
    
    print("\n" + "="*50)
    print("🔍 输入控件分析:")
    print("="*50)
    analyze_input_controls(verify_box)
    
    print("\n✅ 调试完成!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试输入框定位的脚本
"""

import uiautomation as auto
import time

def test_input_box_methods():
    """
    测试各种定位输入框的方法
    """
    print("🔍 正在查找微信添加好友对话框...")
    
    # 查找微信主窗口
    wechat_window = auto.WindowControl(
        searchDepth=1, ClassName="WeChatMainWndForPC", Name="微信"
    )
    
    if not wechat_window.Exists():
        print("❌ 未找到微信主窗口")
        return
    
    # 查找添加好友对话框
    verify_box = wechat_window.WindowControl(
        searchDepth=1, ClassName="WeUIDialog", Name="添加朋友请求"
    )
    
    if not verify_box.Exists():
        print("❌ 未找到添加好友对话框")
        return
    
    print("✅ 找到添加好友对话框")
    
    # 测试各种定位方法
    methods = [
        ("方法1: 直接查找Name='我是.*'", lambda: verify_box.EditControl(Name="我是.*")),
        ("方法2: 查找任何EditControl", lambda: verify_box.EditControl()),
        ("方法3: 深度搜索EditControl", lambda: verify_box.EditControl(searchDepth=5)),
        ("方法4: 查找TextControl", lambda: verify_box.TextControl()),
        ("方法5: 查找包含'我是'的任何控件", lambda: verify_box.Control(NameRegex="我是.*")),
        ("方法6: 查找Value包含'我是'的控件", lambda: verify_box.Control(ValueRegex="我是.*")),
        ("方法7: 查找所有Edit类型控件", lambda: find_all_edit_controls(verify_box)),
    ]
    
    found_controls = []
    
    for method_name, method_func in methods:
        print(f"\n🔄 {method_name}")
        try:
            control = method_func()
            if control and control.Exists():
                print(f"   ✅ 成功! 控件类型: {control.ControlTypeName}, 名称: '{control.Name}'")
                found_controls.append((method_name, control))
            else:
                print(f"   ❌ 失败")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
    
    # 显示所有找到的控件
    if found_controls:
        print(f"\n📋 总共找到 {len(found_controls)} 个控件:")
        for i, (method, control) in enumerate(found_controls, 1):
            print(f"\n控件 {i}: {method}")
            print(f"   类型: {control.ControlTypeName}")
            print(f"   名称: '{control.Name}'")
            print(f"   可见: {control.IsVisible}")
            print(f"   启用: {control.IsEnabled}")
            try:
                if hasattr(control, 'Value'):
                    print(f"   值: '{control.Value}'")
            except:
                pass
            try:
                print(f"   位置: {control.BoundingRectangle}")
            except:
                pass
    else:
        print("\n❌ 没有找到任何输入控件")
    
    # 尝试手动遍历所有子控件
    print(f"\n🔍 手动遍历所有子控件:")
    try:
        children = verify_box.GetChildren()
        for i, child in enumerate(children):
            print(f"   子控件 {i+1}: {child.ControlTypeName} - '{child.Name}' (可见:{child.IsVisible})")
            
            # 如果有孙子控件，也遍历一下
            try:
                grandchildren = child.GetChildren()
                for j, grandchild in enumerate(grandchildren):
                    print(f"     孙子控件 {j+1}: {grandchild.ControlTypeName} - '{grandchild.Name}'")
            except:
                pass
    except Exception as e:
        print(f"   ❌ 遍历子控件失败: {e}")

def find_all_edit_controls(parent):
    """
    查找所有编辑控件
    """
    try:
        children = parent.GetChildren()
        for child in children:
            if "Edit" in child.ControlTypeName:
                return child
            # 递归查找
            result = find_all_edit_controls(child)
            if result:
                return result
    except:
        pass
    return None

if __name__ == "__main__":
    print("🚀 输入框定位测试工具")
    print("请确保已打开微信添加好友对话框")
    input("按回车键开始测试...")
    test_input_box_methods()

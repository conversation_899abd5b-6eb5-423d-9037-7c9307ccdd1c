#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版微信自动添加好友 - 直接复制check_dialog.py的成功方法
"""

import uiautomation as auto
import time
import pyperclip

def add_wechat_contact_simple(custom_message="请求消息", custom_remark=""):
    """
    简化版添加微信好友
    """
    print("🔍 正在查找微信窗口...")
    
    # 查找微信主窗口
    wechat_window = auto.WindowControl(
        searchDepth=1, ClassName="WeChatMainWndForPC", Name="微信"
    )
    
    if not wechat_window.Exists():
        print("❌ 未找到微信主窗口")
        return False
    
    print("✅ 找到微信主窗口")
    
    # 查找添加好友对话框 - 使用check_dialog.py验证成功的方法
    verify_box = wechat_window.WindowControl(
        searchDepth=1, ClassName="WeUIDialog", Name="添加朋友请求"
    )
    
    if not verify_box.Exists():
        print("❌ 未找到添加好友对话框")
        print("请确保已打开添加好友对话框")
        return False
    
    print("✅ 找到添加好友对话框")
    
    # 等待对话框加载
    time.sleep(1)
    
    # 查找按钮
    cancelBtn = verify_box.ButtonControl(Name="取消")
    confirm_btn = verify_box.ButtonControl(Name="确定")
    
    print(f"取消按钮存在: {cancelBtn.Exists()}")
    print(f"确定按钮存在: {confirm_btn.Exists()}")
    
    # 使用check_dialog.py中验证成功的方法查找EditControl
    try:
        print("🔍 查找EditControl...")
        
        # 方法1: 直接查找第一个EditControl（check_dialog.py的方法）
        edit_control = verify_box.EditControl()
        if edit_control.Exists():
            print(f"✅ 找到第一个EditControl: '{edit_control.Name}'")
            
            # 判断是消息框还是备注框
            if edit_control.Name.startswith("我是"):
                print("这是消息输入框")
                input_type = "消息"
                input_text = custom_message
            else:
                print("这是备注输入框")
                input_type = "备注"
                input_text = custom_remark if custom_remark else "测试备注"
            
            # 测试输入 - 使用剪切板方式
            if input_text:
                try:
                    print(f"🔄 开始输入{input_type}: '{input_text}'")
                    edit_control.Click()
                    time.sleep(0.3)
                    
                    # 剪切板方式
                    pyperclip.copy(input_text)
                    edit_control.SendKeys("{Ctrl}a")
                    time.sleep(0.1)
                    edit_control.SendKeys("{Ctrl}v")
                    time.sleep(0.5)
                    
                    print(f"✅ {input_type}输入成功: '{input_text}'")
                    
                except Exception as e:
                    print(f"❌ {input_type}输入失败: {e}")
                    
                    # 备用方法：SendKeys
                    try:
                        print(f"🔄 尝试SendKeys方式...")
                        edit_control.Click()
                        time.sleep(0.2)
                        edit_control.SendKeys("{Ctrl}a")
                        edit_control.SendKeys(input_text)
                        time.sleep(0.5)
                        print(f"✅ SendKeys方式成功!")
                    except Exception as e2:
                        print(f"❌ SendKeys方式也失败: {e2}")
            
            # 查找第二个EditControl（如果有的话）
            try:
                print("🔍 查找第二个EditControl...")
                children = verify_box.GetChildren()
                edit_controls = []
                
                for child in children:
                    if child.ControlTypeName == "EditControl" and child.Exists():
                        edit_controls.append(child)
                
                if len(edit_controls) >= 2:
                    second_edit = edit_controls[1]
                    print(f"✅ 找到第二个EditControl: '{second_edit.Name}'")
                    
                    # 判断第二个输入框的类型
                    if second_edit.Name.startswith("我是"):
                        print("第二个是消息输入框")
                        second_input_type = "消息"
                        second_input_text = custom_message
                    else:
                        print("第二个是备注输入框")
                        second_input_type = "备注"
                        second_input_text = custom_remark if custom_remark else "测试备注"
                    
                    # 输入第二个框
                    if second_input_text and second_input_text != input_text:
                        try:
                            print(f"🔄 开始输入第二个{second_input_type}: '{second_input_text}'")
                            second_edit.Click()
                            time.sleep(0.3)
                            
                            pyperclip.copy(second_input_text)
                            second_edit.SendKeys("{Ctrl}a")
                            time.sleep(0.1)
                            second_edit.SendKeys("{Ctrl}v")
                            time.sleep(0.5)
                            
                            print(f"✅ 第二个{second_input_type}输入成功!")
                            
                        except Exception as e:
                            print(f"❌ 第二个{second_input_type}输入失败: {e}")
                else:
                    print("只找到一个EditControl")
                    
            except Exception as e:
                print(f"查找第二个EditControl时出错: {e}")
        else:
            print("❌ 没有找到任何EditControl")
            return False
            
    except Exception as e:
        print(f"❌ 查找EditControl异常: {e}")
        return False
    
    # 询问用户是否确定
    print("\n" + "="*50)
    print("📋 输入完成!")
    print("请检查输入框内容是否正确")
    print("="*50)
    
    user_choice = input("\n是否要程序自动点击确定？(y/n，直接回车跳过): ").strip().lower()
    
    if user_choice == 'y':
        if confirm_btn.Exists():
            confirm_btn.Click()
            print("✅ 已点击确定按钮")
            return True
        else:
            print("❌ 未找到确定按钮")
            return False
    elif user_choice == 'n':
        if cancelBtn.Exists():
            cancelBtn.Click()
            print("❌ 已点击取消按钮")
            return False
    else:
        print("⏸️  程序结束，请手动操作")
        return True

if __name__ == "__main__":
    print("🚀 简化版微信自动添加好友")
    print("请确保:")
    print("1. 微信已启动并登录")
    print("2. 已打开添加好友对话框")
    print("3. 可以看到输入框")
    
    input("\n按回车键开始...")
    
    # 测试
    add_wechat_contact_simple(
        custom_message="你好，我想和你交流技术问题",
        custom_remark="技术朋友"
    )

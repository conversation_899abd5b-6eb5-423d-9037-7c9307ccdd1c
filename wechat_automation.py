import uiautomation as auto
import time
import cv2
import numpy as np
import pyautogui
import sys
import threading
import subprocess
import random
from datetime import datetime
import os
import pyperclip  # 用于剪切板操作


def print_control_tree(control, depth=0, max_depth=3):
    """
    递归打印控件树结构
    """
    if depth > max_depth:
        return

    indent = "  " * depth
    try:
        if control.Exists():
            print(f"{indent}├─ {control.ControlTypeName}: '{control.Name}' (可见:{control.IsVisible}, 启用:{control.IsEnabled})")

            # 如果是EditControl或TextControl，特别标记
            if "Edit" in control.ControlTypeName or "Text" in control.ControlTypeName:
                print(f"{indent}   ⭐ 这是一个可能的输入控件!")

            # 递归打印子控件
            try:
                children = control.GetChildren()
                for child in children:
                    print_control_tree(child, depth + 1, max_depth)
            except:
                pass
        else:
            print(f"{indent}├─ {control.ControlTypeName}: 不存在")
    except Exception as e:
        print(f"{indent}├─ 错误: {e}")


def debug_control_info(control, name="控件"):
    """
    调试控件信息
    """
    try:
        print(f"🔍 {name} 调试信息:")
        print(f"  - 存在: {control.Exists()}")
        if control.Exists():
            print(f"  - 名称: {control.Name}")
            print(f"  - 类型: {control.ControlTypeName}")
            print(f"  - 可见: {control.IsVisible}")
            print(f"  - 启用: {control.IsEnabled}")
            print(f"  - 位置: {control.BoundingRectangle}")
    except Exception as e:
        print(f"❌ 获取控件信息失败: {e}")


def wait_for_control_ready(control, max_wait=3):
    """
    等待控件准备就绪
    """
    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            if control.Exists() and control.IsVisible and control.IsEnabled:
                return True
        except:
            pass
        time.sleep(0.1)
    return False


def safe_input_text(control, text, method="clipboard"):
    """
    安全的文本输入方法，支持多种输入方式
    :param control: 输入控件
    :param text: 要输入的文本
    :param method: 输入方法 ("clipboard", "sendkeys", "pyautogui")
    :return: 是否成功
    """
    try:
        # 确保控件存在并获取焦点
        if not control.Exists():
            print("❌ 控件不存在")
            return False

        control.Click()
        time.sleep(0.3)

        if method == "clipboard":
            # 方法1: 剪切板方式（推荐）
            pyperclip.copy(text)
            control.SendKeys("{Ctrl}a")
            time.sleep(0.1)
            control.SendKeys("{Ctrl}v")
            time.sleep(0.2)
            print(f"✅ 剪切板方式输入: {text}")
            return True

        elif method == "sendkeys":
            # 方法2: 直接SendKeys
            control.SendKeys("{Ctrl}a")
            time.sleep(0.1)
            control.SendKeys(text)
            time.sleep(0.2)
            print(f"✅ SendKeys方式输入: {text}")
            return True

        elif method == "pyautogui":
            # 方法3: pyautogui键盘输入
            control.SendKeys("{Ctrl}a")
            time.sleep(0.1)
            pyautogui.typewrite(text, interval=0.05)
            time.sleep(0.2)
            print(f"✅ PyAutoGUI方式输入: {text}")
            return True

        elif method == "manual":
            # 方法4: 手动逐字符输入（最后的备用方案）
            control.SendKeys("{Ctrl}a")
            time.sleep(0.1)
            for char in text:
                control.SendKeys(char)
                time.sleep(0.05)
            time.sleep(0.2)
            print(f"✅ 手动逐字符输入: {text}")
            return True

    except Exception as e:
        print(f"❌ 输入失败: {e}")
        return False

    return False


def add_wechat_contact(account, name, custom_message="请求消息", custom_remark=""):
    """
    微信添加单个好友工具（需提前登录微信）
    :param account: 微信号
    :param name: 好友备注
    """
    # 定位微信主窗口
    wechat_window = auto.WindowControl(
        searchDepth=1, ClassName="WeChatMainWndForPC", Name="微信"
    )
    # 激活微信窗口
    wechat_window.SetActive()
    if not wechat_window.Exists():
        raise RuntimeError("微信主窗口未找到，请确认微信已启动并登录")

    # 统一等待时间配置
    wait_sec = 1
    print(f"正在添加: {account}({name})")

    try:
        # 进入通讯录
        wechat_window.ButtonControl(Name="通讯录").Click()
        time.sleep(wait_sec)

        # 查看是否有取消按钮
        cancelBtn = wechat_window.ButtonControl(Name="取消")
        if cancelBtn.Exists():
            cancelBtn.Click()
            time.sleep(wait_sec)

        # 打开添加界面
        wechat_window.ButtonControl(Name="添加朋友").Click()
        time.sleep(wait_sec * 2)  # 此界面加载需要更多时间
        # 获取输入框控件
        search_box = wechat_window.EditControl(Name="微信号/手机号")
        # 获取焦点 无法获取焦点
        # search_box.SetFocus()
        # 换成点击
        search_box.Click()
        # 全选输入框
        search_box.SendKeys("{Ctrl}a")
        # 输入微信号
        search_box.SendKeys(account)
        time.sleep(wait_sec)

        # 点击搜索按钮
        search_btn = wechat_window.ListItemControl(
            NameRegex=f"搜索[:：]\\s*{account}", searchDepth=10, timeout=15
        )
        # 点击搜索按钮
        search_btn.Click()
        time.sleep(wait_sec)
        # 无结果
        no_result = wechat_window.ListItemControl(
            Name="无法找到该用户，请检查你填写的账号是否正确。",
            # 限制搜索层级
            foundIndex=1,  # 未找到时返回None
            timeout=0,
        )
        # 添加到通讯录面板

        print(f"no_result: {no_result.Exists()}")
        # 无结果是进入
        if no_result.Exists():
            print(f"❌ 未找到用户: {account}")
            return False
        # 定位添加通讯录按钮
        click_image("./templates/add_friend_button.png")
        # 申请消息按钮
        verify_box = wechat_window.WindowControl(
            searchDepth=1, ClassName="WeUIDialog", Name="添加朋友请求"
        )
        # 验证消息框是否存在
        if not verify_box.Exists():
            print("❌ 添加朋友对话框不存在")
            return False

        print("✅ 找到添加朋友对话框")

        # 取消按钮
        cancelBtn = verify_box.ButtonControl(Name="取消")
        # 确定按钮
        confirm_btn = verify_box.ButtonControl(Name="确定")

        # 等待对话框完全加载
        print("⏳ 等待对话框完全加载...")
        time.sleep(1.5)

        # 使用check_dialog.py中验证有效的最简单方法
        print("🔍 使用最简单有效的方法查找输入框...")

        remarkBox = None  # 备注输入框
        messageBox = None  # 消息输入框

        try:
            # 方法1: 直接查找第一个EditControl（和check_dialog.py一样）
            first_edit = verify_box.EditControl()
            if first_edit.Exists():
                print(f"✅ 找到第一个EditControl: '{first_edit.Name}'")

                # 判断这是哪个输入框
                if first_edit.Name.startswith("我是"):
                    messageBox = first_edit
                    print("   这是消息输入框")
                else:
                    remarkBox = first_edit
                    print("   这是备注输入框")

            # 方法2: 查找所有EditControl（简单遍历）
            print("🔍 查找所有EditControl...")
            children = verify_box.GetChildren()
            all_edits = []

            for child in children:
                if child.ControlTypeName == "EditControl" and child.Exists():
                    all_edits.append(child)
                    print(f"   找到EditControl: '{child.Name}'")

            # 如果找到多个，按特征分配
            if len(all_edits) >= 2:
                for edit in all_edits:
                    if edit.Name.startswith("我是"):
                        messageBox = edit
                        print(f"   ✅ 消息输入框: '{edit.Name}'")
                    else:
                        remarkBox = edit
                        print(f"   ✅ 备注输入框: '{edit.Name}'")
            elif len(all_edits) == 1:
                edit = all_edits[0]
                if edit.Name.startswith("我是"):
                    messageBox = edit
                    print(f"   ✅ 只找到消息输入框: '{edit.Name}'")
                else:
                    remarkBox = edit
                    print(f"   ✅ 只找到备注输入框: '{edit.Name}'")

        except Exception as e:
            print(f"❌ 查找输入框失败: {e}")

        # 显示最终结果
        print(f"\n📋 输入框查找结果:")
        print(f"备注输入框: {'✅ 找到' if remarkBox else '❌ 未找到'}")
        print(f"消息输入框: {'✅ 找到' if messageBox else '❌ 未找到'}")

        # 输入备注（如果提供了备注且找到了备注输入框）
        remark_success = True  # 默认成功，因为备注是可选的
        if custom_remark and remarkBox is not None:
            print(f"\n🔄 开始输入备注: '{custom_remark}'")
            methods = ["clipboard", "sendkeys"]

            for method in methods:
                print(f"🔄 尝试使用 {method} 方式输入备注...")
                if safe_input_text(remarkBox, custom_remark, method):
                    remark_success = True
                    print(f"✅ 成功输入备注: '{custom_remark}'")
                    break
                time.sleep(0.5)
            else:
                print("❌ 备注输入失败")
                remark_success = False
        elif custom_remark and remarkBox is None:
            print("⚠️  提供了备注但未找到备注输入框")

        # 输入消息（如果找到了消息输入框）
        message_success = False
        if messageBox is not None:
            print(f"\n🔄 开始输入消息: '{custom_message}'")
            methods = ["clipboard", "sendkeys", "pyautogui", "manual"]

            for method in methods:
                print(f"🔄 尝试使用 {method} 方式输入消息...")
                if safe_input_text(messageBox, custom_message, method):
                    message_success = True
                    print(f"✅ 成功输入消息: '{custom_message}'")
                    break
                time.sleep(0.5)
        else:
            print("❌ 未找到消息输入框，跳过消息输入")

        if not message_success and messageBox is not None:
            print("❌ 消息输入失败")

        # 显示输入结果摘要
        print("\n" + "="*60)
        print("📋 输入结果摘要:")
        print("="*60)
        if custom_remark:
            print(f"备注输入: {'✅ 成功' if remark_success else '❌ 失败'} - '{custom_remark}'")
        else:
            print("备注输入: ⏭️ 跳过（未提供备注）")
        print(f"消息输入: {'✅ 成功' if message_success else '❌ 失败'} - '{custom_message}'")
        print("="*60)

        # 暂停，让用户检查
        print("⏸️  输入完成，程序暂停")
        print("请手动检查输入内容是否正确")

        # 可选：等待用户确认
        user_choice = input("\n是否要程序自动点击确定？(y/n，直接回车跳过): ").strip().lower()

        if user_choice == 'y':
            if confirm_btn.Exists():
                confirm_btn.Click()
                print("✅ 已发送好友申请")
                time.sleep(wait_sec)
                return True
            else:
                print("❌ 未找到确定按钮")
                return False
        elif user_choice == 'n':
            if cancelBtn.Exists():
                cancelBtn.Click()
                print("❌ 用户选择取消申请")
                return False
        else:
            print("⏸️  程序结束，请手动操作")
            return True

    except Exception as e:

        print(f"添加 {account} 失败: {str(e).split('。')[0]}")
        return False


# 点击任意窗体的确定按钮
def click_confirm_button(window, wait_sec=1):
    window.ButtonControl(Name="确定").Click()
    time.sleep(wait_sec)
    return True


# opencv点击按钮
def click_by_template(template_path, confidence=0.9):
    # 截屏并转换颜色空间
    screenshot = pyautogui.screenshot()
    screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

    # 加载模板
    template = cv2.imread(template_path, cv2.IMREAD_COLOR)
    if template is None:
        raise FileNotFoundError(f"模板图片不存在: {template_path}")

    # 多尺度匹配
    res = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)

    if max_val >= confidence:
        # 计算中心点坐标
        h, w = template.shape[:2]
        center_x = max_loc[0] + w // 2
        center_y = max_loc[1] + h // 2

        # 模拟点击
        pyautogui.click(center_x, center_y)
        return True
    return False


# 模拟人类鼠标移动轨迹
def human_like_move_to(x, y):
    current_x, current_y = pyautogui.position()
    steps = random.choice([2, 3, 4])
    for i in range(1, steps + 1):
        ratio = i / steps
        intermediate_x = int(current_x * (1 - ratio) + x * ratio)
        intermediate_y = int(current_y * (1 - ratio) + y * ratio)
        jitter_x = random.randint(-3, 3)
        jitter_y = random.randint(-3, 3)
        pyautogui.moveTo(intermediate_x + jitter_x, intermediate_y + jitter_y)
        time.sleep(random.uniform(0.1, 0.3))


# 模拟人类点击动作
def human_like_click(x=None, y=None):
    current_x, current_y = pyautogui.position()
    target_x = x if x is not None else current_x
    target_y = y if y is not None else current_y
    human_like_move_to(target_x, target_y)
    time.sleep(random.uniform(0.2, 1))
    final_x = target_x + random.randint(-8, 8)
    final_y = target_y + random.randint(-8, 8)
    pyautogui.click(final_x, final_y)


# 根据pyauto点击
def click_image(img_path, confidence=0.8, retries=3, delay=2):
    for i in range(retries):
        location = pyautogui.locateOnScreen(img_path)
        if location:
            center = pyautogui.center(location)
            human_like_click(center.x, center.y)
            return True
        time.sleep(delay)
    return False


# 使用示例
if __name__ == "__main__":
    # 添加单个好友示例
    # add_wechat_contact(微信号, 备注名, 自定义消息, 自定义备注)
    add_wechat_contact(
        account="dididi",
        name="技术好友",
        custom_message="你好，我想和你交流技术问题",
        custom_remark="技术大佬"
    )

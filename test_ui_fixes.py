# -*- coding: utf-8 -*-
"""
测试UI修复和新功能
"""
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_redislite():
    """测试RedisLite功能"""
    print("🧪 测试 RedisLite...")
    
    try:
        from redislite import get_redis, get_log_manager
        
        redis = get_redis()
        log_manager = get_log_manager()
        
        # 测试基本功能
        redis.set("test", {"name": "测试", "value": 123})
        result = redis.get("test")
        print(f"✅ Redis基本功能: {result}")
        
        # 测试日志管理
        log_manager.add_log("测试日志", "info", "test")
        logs = log_manager.get_logs(1)
        print(f"✅ 日志管理功能: {len(logs)} 条日志")
        
        return True
    except Exception as e:
        print(f"❌ RedisLite测试失败: {e}")
        return False

def test_database():
    """测试数据库功能"""
    print("\n🧪 测试数据库功能...")
    
    try:
        from sqlite3_util import (
            init_database, 
            add_user_log, 
            query_user_logs, 
            check_user_added
        )
        
        # 初始化数据库
        init_result = init_database()
        print(f"✅ 数据库初始化: {init_result}")
        
        # 测试添加日志
        log_result = add_user_log(
            wechat_id="test_user",
            verify_msg="测试消息",
            status=1,
            remark_name="测试备注"
        )
        print(f"✅ 添加日志: {log_result}")
        
        # 测试查询日志
        logs = query_user_logs(limit=5)
        print(f"✅ 查询日志: {len(logs)} 条记录")
        
        # 测试检查用户
        is_added = check_user_added(wechat_id="test_user")
        print(f"✅ 检查用户状态: {is_added}")
        
        return True
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_apis():
    """测试API功能"""
    print("\n🧪 测试API功能...")
    
    try:
        from apis import API
        
        api = API()
        
        # 测试获取用户日志
        logs_result = api.get_user_logs()
        print(f"✅ 获取用户日志API: {logs_result.get('success', False)}")
        
        # 测试微信状态检查
        status_result = api.check_wechat_status()
        print(f"✅ 微信状态检查API: {status_result.get('success', False)}")
        
        return True
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试UI修复和新功能...\n")
    
    results = []
    
    # 测试各个模块
    results.append(("RedisLite", test_redislite()))
    results.append(("数据库", test_database()))
    results.append(("APIs", test_apis()))
    
    # 输出测试结果
    print("\n📊 测试结果汇总:")
    print("=" * 50)
    
    success_count = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:15} {status}")
        if result:
            success_count += 1
    
    print("=" * 50)
    print(f"总计: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("\n🎉 所有测试通过！UI修复和新功能已就绪。")
    else:
        print(f"\n⚠️  有 {len(results) - success_count} 项测试失败，请检查相关功能。")
    
    return success_count == len(results)

if __name__ == "__main__":
    main()
